<template>
  <div class="welcome">
    <div class="top">
      <!-- <div class="logo">
        <img class="img-logo" src="@/assets/9_9/images/logo.png" alt="" />
      </div> -->
      <!-- <div class="gifBox">
        <img :src="gifLst[gifIndex].img" :style="gifLst[gifIndex].style">
      </div> -->
      <div> 
        <img class="my-img" src="@/assets/hehua/welcome/bg_top.png" alt="" v-if="!isTest" />
      </div>
      
      <!-- <img class="my-img" src="@/assets/9_9/images/round.gif" alt="" /> -->
      <div class="video-wraper">
        <div class="videoBox">
          <div class="my-video">
            <base-video
              :extraStyle="{
                transform: 'scaleX(-1) rotate(90deg) scale(1.35)',
                zIndex: '998',
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%'
              }"
              cameraType="snap"
              @initFinished="handleInitFinishedSnap"
            ></base-video>
            <base-video
              :extraStyle="{
                transform: 'scaleX(-1) rotate(90deg) scale(1.35)',
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%'
              }"
              @initFinished="handleInitFinished"
            ></base-video>
          </div>
          <div class="img-inner" id="videoBox">
            <img class="my-img" :src="canvasImg" />
          </div>
          <div class="img-inner-2">
            <img class="true-img" :src="capturedImage" ref="imgDom" />
          </div>
          <div v-if="countdown != 0" class="count-numer">
            {{ countdown }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="!isShowActiveBottom" class="bottom">
      <div class="buttonBox">
        <div class="leftButton">
          <div class="my-img">
            <img src="@/assets/welcome/images/btn-circle.png" />
            <div class="float-area">
              <div class="operate-content">
                <div class="operate-content-top">
                  <div class="top-click" @click.stop="onHotzoneClick('top')"></div>
                </div>
                <div class="operate-content-center">
                  <div class="left-click" @click.stop="onHotzoneClick('left')"></div>
                  <div class="center-click" @click.stop="onHotzoneClick('center')"></div>
                  <div class="right-click" @click.stop="onHotzoneClick('right')"></div>
                </div>
                <div class="operate-content-bottom">
                  <div class="bottom-click" @click.stop="onHotzoneClick('bottom')"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="user-agent">
            <base-check-box v-model="checked">
              <span style="color: rgba(0, 0, 0, 0); font-size: 2px">1</span>
              <img
                class="my-img"
                src="@/assets/welcome/images/user-text.png"
                @click="handleClickUserProc"
              />
            </base-check-box>
          </div>
        </div>
        <div class="right-video">
          <div class="guang"></div>

          <div class="my-bottom-video">
            <div class="video-content">
              <video
                src="@/assets/video/480.mp4"
                class="viewVideo"
                autoplay
                loop
                playsinline
                muted
                ref="viewVideo"
              ></video>
            </div>
          </div>
        </div>
      </div>
    </div>
    <activity-bottom
      v-else
      :countdown="5"
      :isHasBuyBtn="isHasBuyBtn"
      :isHasSureBtn="isHasSureBtn"
      :sureLoading="sureBtnLoading"
       :themeImg="{
        bgImg:bgBottom,
        btnImg:btnBg,
        outerCircleBgImg:btnWraper,
        reTakeBtnTextImg:retakeText,
        backBtnTextImg:backText,
        purchaseTextImg:purchaseText,
        sureBtnTextImg:sureText,
      }"
      @countDecrease="handleCountDecrease"
      @countFinished="handleCountFinished"
      @handleSure="handleReceiveSure"
      @handleRetake="handleReceiveRetake"
      @handleBack="handleReceiveBack"
    >
    </activity-bottom>
    <base-dialog v-model="isShowProcDialog" size="70%" :modal="false">
      <proc></proc>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from '@/components/base/Dialog/index.vue'
import BaseCheckBox from '@/components/base/CheckBox/index.vue'
import Proc from '@/views/popfifi/proc.vue'
import { ElCheckbox, ElMessage } from 'element-plus'
import { calculateFaceQuality } from '@/utils/faceComputedFn'
import BaseVideo from '@/components/base/Video/index.vue'
import ActivityBottom from '@/components/activity/Bottom/index.vue'
import { useSinglePhotoStore } from '@/store/single'
import { getPhotoFace } from '@/apis/face'
import { getImageSize, base64ToBlob } from '@/utils'
import bus from '@/utils/bus'
import { generateUUID } from '@/utils/uuid'
import { snapdom } from '@zumer/snapdom'
import bgBottom from  '@/assets/hehua/welcome/bg_bottom.png'
import btnBg from  '@/assets/hehua/bottom/btn-bg.png'
import btnWraper from  '@/assets/hehua/welcome/btn-wraper.png'
import retakeText from  '@/assets/hehua/bottom/retake-text.png'
import backText from  '@/assets/hehua/bottom/back-text.png'
import purchaseText from  '@/assets/hehua/bottom/purchase-text.png'
import sureText from  '@/assets/hehua/bottom/sure-text.png'
//循环图
import f1 from '@/assets/9_9/images/hdd.gif'
import f2 from '@/assets/9_9/images/ldd.gif'
import f3 from '@/assets/9_9/images/solder.gif'
import f4 from '@/assets/9_9/images/chaoliu.gif'
import f5 from '@/assets/9_9/images/dll.gif'

export default {
  components: {
    BaseDialog,
    BaseCheckBox,
    Proc,
    ElCheckbox,
    BaseVideo,
    ActivityBottom
  },
  data() {
    return {
      isTest: true,
      isShowProcDialog: false,
      checked: true, // 新增checked状态
      snapVideoRef: null,
      snapCameraInited: false,
      commonVideoRef: null,
      commonCameraInited: false,
      isHasBuyBtn: false,
      isHasSureBtn: false,
      countdown: 0,
      isShowActiveBottom: false,
      currentSex: 'male',
      canvasImg: '',
      capturedImage: '',
      myCanvas: null,
      faceArr: [],
      sureBtnLoading: false,
      testImg: '',
      showTest: false,
      fileUploadLoading: false,
      gifLst: [
        { img: f1, style: 'bottom: 60%;left:-5vw;width:25vw;' },
        { img: f2, style: 'bottom: 40%;left:-5vw;width:25vw;' },
        { img: f3, style: 'bottom: 60%;right:-5vw;width:25vw;' },
        { img: f4, style: 'bottom: 30%;right:-11vw;width:30vw;' },
        { img: f5, style: 'bottom: 0vw;left:-5vw;width:40vw;' }
      ],
      gifIndex: 0,
      gifInterval: null,
       bgBottom,
      btnBg,
      btnWraper,
      retakeText,
      backText,
      purchaseText,
      sureText,
    }
  },
  // mounted() {
  //   this.gifInterval = setInterval(() => {
  //     this.gifIndex = (this.gifIndex + 1) % this.gifLst.length
  //   }, 3000)
  // },
  // unmounted() {
  //   this.gifInterval && clearInterval(this.gifInterval)
  // },
  watch: {
    commonCameraInited(newVal) {
      if (newVal) {
        console.log('bianhua')
        bus.emit('cameraInitFinished')
      }
    }
  },

  methods: {
    // 收到basevideo初始化完成的回调
    handleInitFinishedSnap(videoRef) {
      this.snapVideoRef = videoRef
      //  下面this.snapCameraInited是为了底部有公共操作区自动倒计时拍照使用的。
      // 如果页面上不涉及公共操作区自动倒计时拍照的功能  this.snapCameraInited = true;这一句话不用写，watch也不用监听
      //  this.snapCameraInited = true;
    },
    handleInitFinished(videoRef) {
      console.log('init', videoRef)
      this.commonVideoRef = videoRef
      this.myCanvas = document.createElement('canvas')
    },
    onHotzoneClick(zone) {
      let that = this
      if (!this.checked) {
        ElMessage.error('请先同意用户协议须知再继续!')
        return false
      }
      console.log('点击热区:', zone)
      if (zone == 'top') {
        this.$router.push('/takePhoto9_9')
      }
      if (zone == 'bottom') {
        this.$router.push('/family-home')
      }
      if (zone == 'left') {
        this.isShowActiveBottom = true
        this.currentSex = 'male'
        if (this.isTest){
          return this.handleClickShibei()
        }
        this.$nextTick(() => {
          that.commonCameraInited = true
        })
      }
      if (zone == 'right') {
        this.isShowActiveBottom = true
        this.currentSex = 'female'
        if (this.isTest){
          return this.handleClickShibei()
        }
        this.$nextTick(() => {
          that.commonCameraInited = true
        })
      }
    },
    handleClickUserProc() {
      this.isShowProcDialog = true
    },
    capturePhoto() {
      let that = this
      const video = this.commonVideoRef
      video.pause()
      this.snapVideoRef && this.snapVideoRef.pause()
      const canvas = this.myCanvas || document.createElement('canvas')
      // canvas.width = video.videoHeight
      // canvas.height = video.videoWidth
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight)
      ctx.restore() // 恢复画布状态
      // 保存base64图片数据
      this.canvasImg = canvas.toDataURL('image/jpeg')
      const targetElement = document.getElementById('videoBox')
      console.log(targetElement, '拿到了')
      return new Promise((resolve, reject) => {
        // // 获取原始尺寸
        const originalWidth = targetElement.offsetWidth
        const originalHeight = targetElement.offsetHeight
        // 设置最大尺寸
        const maxSize = 1400
        let scale = 1
        // 计算缩放比例
        if (originalWidth > maxSize || originalHeight > maxSize) {
          scale = Math.min(maxSize / originalWidth, maxSize / originalHeight)
        }
        this.$nextTick(async () => {
          const img = await snapdom.toJpg(targetElement, { dpr: scale })
          if (img) {
            resolve(img.src)
            that.capturedImage = img.src
          }
        })
      })
    },
    handleCountDecrease(count) {
      console.log('走了', count)
      this.countdown = count
    },
    handleCountFinished() {
      if (this.isTest){
        this.isHasSureBtn = true
        this.sureBtnLoading = true
        
        this.handleClickShibei()
        return
      }
      if (this.commonVideoRef) {
        this.isHasSureBtn = true
        this.sureBtnLoading = true
        this.handleClickShibei()
      }
    },
    async handleReceiveSure() {
      const item = this.faceArr[0]
      const faceData = await this.captureFaceImage({
        left: item.left,
        top: item.top,
        width: item.width,
        height: item.height
      })
      const useSingleStore = useSinglePhotoStore()
      if (faceData) {
        const file = new File([base64ToBlob(faceData)], `${generateUUID()}.jpg`, {
          type: 'image/jpeg'
        })
        
        useSingleStore.setSinglePhotoData({
          photo: faceData,
          photoFile: file,
          gender: this.currentSex
        })
        console.log(useSingleStore, 'store')
      }

      // 存储到store中

      // 后续处理逻辑...
      // // 跳转到下个路由
      // this.$router.push({ path: '/rebuild2-themeSelect' })
      let styleLst =  useSingleStore.stylesData
      let sex = this.currentSex == 'female' ? 0 : 1
      //sex list
      let sameSexs = styleLst.filter((i) => i.sex == sex)
      let notSameSexs = styleLst.filter((i) => i.sex != sex)
      let len = sameSexs.length - 5
      if (len === 0) {
      } else if (len > 0) {
        sameSexs.splice(0, 5)
      } else if (len < 0) {
        sameSexs.push(...notSameSexs.slice(0, -len))
      }
      
      this.$router.push({
        path: '/rebuild2-loading',
        query: { code: sameSexs[0]['code_2d'] },
        state: {
          subjectFile: useSingleStore.singlePhotoData.photoFile,
          // templateFile: templateFile,
          codes: sameSexs.map((i) => i['code_2d']),
          code: sameSexs[0]['code_2d'],
          gender: this.currentSex
        }
      })

    },
    handleReceiveRetake() {
      if (this.commonVideoRef) {
        this.initRetake()
        this.commonVideoRef.play()
        this.snapVideoRef && this.snapVideoRef.play()
      }
    },
    handleReceiveBack() {
      this.initAll()
      this.commonVideoRef && this.commonVideoRef.play()
      this.snapVideoRef && this.snapVideoRef.play()
    },
    async captureFaceImage(faceBox) {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          // 使用原始图像尺寸计算
          const originalWidth = img.naturalWidth
          const originalHeight = img.naturalHeight

          // 计算实际缩放比例（显示尺寸/原始尺寸）
          const displayScale = this.$refs.imgDom.width / originalWidth

          // 将显示坐标转换为原始图像坐标
          const originLeft = faceBox.left / displayScale
          const originTop = faceBox.top / displayScale
          const originWidth = faceBox.width / displayScale
          const originHeight = faceBox.height / displayScale

          // 应用1.3倍放大
          const scaledWidth = originWidth * 1.35
          const scaledHeight = originHeight * 1.35
          const scaledLeft = originLeft - (scaledWidth - originWidth) / 2
          const scaledTop = originTop - (scaledHeight - originHeight) / 2

          // 边界检查
          const finalLeft = Math.max(0, scaledLeft)
          const finalTop = Math.max(0, scaledTop)
          const finalRight = Math.min(originalWidth, scaledLeft + scaledWidth)
          const finalBottom = Math.min(originalHeight, scaledTop + scaledHeight)
          const finalWidth = finalRight - finalLeft
          const finalHeight = finalBottom - finalTop

          canvas.width = finalWidth
          canvas.height = finalHeight

          ctx.drawImage(
            img,
            finalLeft,
            finalTop,
            finalWidth,
            finalHeight,
            0,
            0,
            finalWidth,
            finalHeight
          )
          resolve(canvas.toDataURL('image/jpeg'))
        }
        img.src = this.capturedImage // 使用原始图像数据
      })
    },

    async handleClickShibei() {
      console.log('开始识别流程')
      let res = null

      try {
        if (this.isTest) {
          // 测试模式下触发文件上传并等待用户选择文件
          console.log('测试模式：等待用户选择文件...')
          let pic = await this.handleFileUpload()
          console.log(pic)
          console.log('文件上传完成，继续处理...')
        } else {
          res = await this.capturePhoto()
        }

        // 确保有图片数据后再继续处理
        if (!this.capturedImage) {
          ElMessage.error('未获取到图片数据，请重试')
          this.sureBtnLoading = false
          return
        }

        console.log('开始获取图片信息...')
        const imgInfo = await getImageSize(this.capturedImage)
        console.log('图片信息获取完成')

        const scale = this.$refs.imgDom.width / imgInfo.width
        console.log(this.$refs.imgDom.width, imgInfo.width, scale, 'scale.value')

        console.log('开始人脸检测...')
        const detections = await getPhotoFace({ base64: this.capturedImage })
        this.handleDections(detections, scale)
      } catch (error) {
        console.error('识别流程出错:', error)
        ElMessage.error('处理失败: ' + (error.message || '未知错误'))
        this.sureBtnLoading = false
      }
    },

    handleDections(detections, scale) {
      console.log(!detections || !detections.length, 'shenme')
      if (!detections || !detections.length) {
        this.isHasSureBtn = false
        this.sureBtnLoading = false
        ElMessage.error('未检测到人脸,请重试!')
        return false
      }
      const sorted = detections.map((d) => ({
        ...d,
        imageHeight: d.alignedRect._imageDims._height,
        imageWidth: d.alignedRect._imageDims._width,
        area: d.detection._box._width * d.detection._box._height,
        score: d.detection._score,
        quality: calculateFaceQuality(d.landmarks)
      }))

      //  取眼睛清晰度>30的，score>20, 面积1280*720 的2万
      const filterArr = sorted.filter((sitem) => {
        console.log(sitem, 'guo')
        const filterArea = ((sitem.imageHeight * sitem.imageWidth) / (1280 * 720)) * 20000
        if (sitem.score * 100 > 30 && sitem.quality.score > 30 && sitem.area > filterArea) {
          return sitem
        }
      })

      if (!filterArr.length) {
        this.isHasSureBtn = false
        this.sureBtnLoading = false
        ElMessage.error('未检测到人脸,请重试!')
        return false
      }
      if (filterArr.length && filterArr.length != 1) {
        // 按照sitem.score和sitem.quality.score和sitem.area排序
        filterArr.sort((a, b) => {
          return b.quality.score - a.quality.score || b.area - a.area || b.score - a.score
        })
      }
      this.faceArr = filterArr.map((item) => {
        return {
          width: item.alignedRect._box._width * scale,
          height: item.alignedRect._box._height * scale,
          top: item.alignedRect._box._y * scale,
          left: item.alignedRect._box._x * scale
        }
      })
      this.sureBtnLoading = false
      if (this.isTest){
        this.handleReceiveSure()
      }
    },

    initRetake() {
      this.faceArr = []
      this.canvasImg = ''
      this.capturedImage = ''
      this.isHasSureBtn = false
      this.countdown = 0
      this.sureBtnLoading = false
    },
    initAll() {
      this.faceArr = []
      this.canvasImg = ''
      this.capturedImage = ''
      this.isShowActiveBottom = false
      this.commonCameraInited = false
      this.isHasSureBtn = false
      this.countdown = 0
      this.sureBtnLoading = false
    },

    // 文件上传处理方法
    async handleFileUpload() {
      this.fileUploadLoading = true

      return new Promise((resolve, reject) => {
        // 创建隐藏的文件输入元素
        const fileInput = document.createElement('input')
        fileInput.type = 'file'
        fileInput.accept = 'image/*'
        fileInput.style.display = 'none'

        fileInput.onchange = async (event) => {
          const file = event.target.files[0]
          if (!file) {
            this.fileUploadLoading = false
            reject(new Error('未选择文件'))
            return
          }

          // 验证文件类型
          if (!file.type.startsWith('image/')) {
            this.fileUploadLoading = false
            ElMessage.error('请选择图片文件')
            reject(new Error('文件类型不正确'))
            return
          }

          // 验证文件大小 (限制为10MB)
          const maxSize = 10 * 1024 * 1024
          if (file.size > maxSize) {
            this.fileUploadLoading = false
            ElMessage.error('文件大小不能超过10MB')
            reject(new Error('文件过大'))
            return
          }

          try {
            // 使用FileReader读取文件并转换为base64
            const reader = new FileReader()
            reader.onload = (e) => {
              this.capturedImage = e.target.result
              this.fileUploadLoading = false
              console.log('文件上传成功，已设置capturedImage')
              ElMessage.success('图片上传成功')
              resolve(e.target.result)
            }
            reader.onerror = () => {
              this.fileUploadLoading = false
              ElMessage.error('文件读取失败')
              reject(new Error('文件读取失败'))
            }
            reader.readAsDataURL(file)
          } catch (error) {
            this.fileUploadLoading = false
            console.error('文件处理错误:', error)
            ElMessage.error('文件处理失败')
            reject(error)
          } finally {
            // 清理DOM元素
            if (document.body.contains(fileInput)) {
              document.body.removeChild(fileInput)
            }
          }
        }

        fileInput.oncancel = () => {
          this.fileUploadLoading = false
          if (document.body.contains(fileInput)) {
            document.body.removeChild(fileInput)
          }
          reject(new Error('用户取消选择文件'))
        }

        // 添加到DOM并触发点击
        document.body.appendChild(fileInput)
        fileInput.click()
      })
    }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'DouyinSansBold';
  src: url('@/assets/fonts/DouyinSansBold.ttf') format('truetype');
}
@property --angle {
  syntax: '<angle>';
  inherits: false;
  initial-value: 0deg;
}
@keyframes rotate {
  to {
    --angle: 360deg;
  }
}
.welcome {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'DouyinSansBold', sans-serif;

  .top {
    width: 100%;
    height: 80%;
    position: relative;
    overflow: hidden;
    .gifBox {
      height: 100%;
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 200;
      img {
        width: 30vw;
        // height: 40vw;
        object-fit: cover;
        position: absolute;
        // left: 0;
        // top: 50%;
      }
    }
    .logo {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      z-index: 30;
      .img-logo {
        width: 100%;
      }
    }
    .my-img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%; // 修改为100%填充
      height: 100%; // 修改为100%填充
      z-index: 20;
    }
    .video-wraper {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0%;
      top: 0%;
      z-index: 19;
      display: flex;
      justify-content: center;
      align-items: center;
      .videoBox {
        width: 100%;
        height: 80%;
        position: absolute;
        .my-video {
          width: 100%;
          height: 100%;
          overflow: hidden;
          position: absolute;
          left: 0;
          top: 0;
        }
        .img-inner {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          overflow: hidden;
          .my-img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1) rotate(90deg) scale(1.35);
          }
        }
        .img-inner-2 {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: -10;
          .true-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .count-numer {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 507rem;
          color: #fff;
          font-weight: 700;
          z-index: 999;
        }
      }
    }
  }
  .bottom {
    height: 20%;
    width: 100%;
    box-sizing: border-box;
    background: url('@/assets/hehua/welcome/bg_bottom.png') no-repeat center center;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    .buttonBox {
     width: 98%;
     height: 100%;
     display: flex;
     justify-content: center;
     padding: 0rem 40rem 0rem 50rem;
     box-sizing: border-box;
     align-items: center;
     background: url('@/assets/hehua/welcome/btn-wraper.png') no-repeat center center;
     background-size: contain;
      .leftButton {
        flex: 1;
        height: 85%;
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
        .my-img {
          height: 100%;
          position: relative;
          img {
            max-height: 100%;
            max-width: 100%;
          }
          .float-area {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            .operate-content {
              width: 83%;
              height: 83%;
              display: flex;
              flex-direction: column;
              &-top {
                height: 34.85%;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                .top-click {
                  width: 31.85%;
                  height: 100%;
                }
              }
              &-center {
                flex: 1;
                width: 100%;
                display: flex;
                .left-click {
                  width: 34.85%;
                  height: 100%;
                }
                .center-click {
                  flex: 1;
                  height: 100%;
                }
                .right-click {
                  width: 34.85%;
                  height: 100%;
                }
              }
              &-bottom {
                height: 34.85%;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                .bottom-click {
                  width: 31.85%;
                  height: 100%;
                }
              }
            }
          }
        }
        .user-agent {
          flex: 1;
          height: 100%;
          font-size: 45rem;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          padding-bottom: 30rem;
          .my-img {
            height: 70rem;
            width: auto;
          }
        }
      }
      .right-video {
        width: 380rem;
        height: 380rem;
        // border: 8rem solid rgba(255, 235, 59, 1);
        box-sizing: border-box;
        border-radius: 17rem;
        filter: drop-shadow(0 0 10rem hsl(162, 100%, 58%)) drop-shadow(0 0 20rem hsl(270 63% 58%));
        position: relative;
        margin-right: 30rem;
        .guang {
          width: 100%;
          height: 100%;
          border-radius: 17rem;
          clip-path: polygon(
            0% 0%,
            /* 外框左上 */ 100% 0%,
            /* 外框右上 */ 100% 100%,
            /* 外框右下 */ 0% 100%,
            /* 外框左下 */ 0% 0%,

            /* 闭合外框 */ /* 内框裁剪（从10%到90%） */ 3% 3%,
            /* 内框左上 */ 3% 97%,
            /* 内框左下 */ 97% 97%,
            /* 内框右下 */ 97% 3%,
            /* 内框右上 */ 3% 3% /* 闭合内框 */
          );
          background: conic-gradient(
            from var(--angle),
            hsl(162, 100%, 58%),
            hsl(270, 63%, 58%),
            hsl(162, 100%, 58%)
          );
          animation: rotate 3s infinite linear;
        }
        .my-bottom-video {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 999;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 17rem;
          .video-content {
            width: 94%;
            height: 94%;
            border-radius: 8rem;
            overflow: hidden;
            video {
              width: 100%;
              height: 100%;
              //       transform: translateZ(0); // 触发GPU加速
              // will-change: transform;   // 提示浏览器优化
            }
          }
        }
      }
    }
  }
}
</style>
