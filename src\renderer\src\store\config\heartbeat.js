import {ref,computed} from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'

let timeout = null
const startHeart = e=>{
    axios.get('/api/v1.0/device/heartbeat').then(e=>{
      timeout = setTimeout(e=>{
        startHeart()
      },60*1000)
    })
}

const endHeart = e=>{
    clearTimeout(timeout)
}

// 全家福拍照相关
export const useSinglePhotoStore = defineStore('heartBeat',()=>{
    return { startHeart, endHeart}
})